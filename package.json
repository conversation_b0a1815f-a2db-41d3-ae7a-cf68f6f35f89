{"name": "wealthfolio-app", "private": true, "version": "1.1.5", "type": "module", "scripts": {"dev": "vite", "debug": "RUST_LOG=debug pnpm tauri dev", "tsc": "tsc -b", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@fontsource/ibm-plex-mono": "^5.2.6", "@hookform/resolvers": "^3.9.1", "@internationalized/date": "^3.8.0", "@number-flow/react": "^0.5.10", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.62.7", "@tanstack/react-table": "^8.20.6", "@tanstack/react-virtual": "^3.11.1", "@tauri-apps/api": "^2.4.1", "@tauri-apps/plugin-dialog": "~2.2.1", "@tauri-apps/plugin-fs": "~2.2.1", "@tauri-apps/plugin-log": "~2.3.1", "@tauri-apps/plugin-shell": "~2.2.1", "@tauri-apps/plugin-updater": "~2.7.0", "@tauri-apps/plugin-window-state": "~2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.6.3", "lodash": "^4.17.21", "lucide-react": "^0.467.0", "papaparse": "^5.4.1", "react": "^18.3.1", "react-aria-components": "^1.8.0", "react-day-picker": "^8.7.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-router-dom": "^7.5.2", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "zod": "^3.24.1"}, "devDependencies": {"@tauri-apps/cli": "^2.4.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.17.16", "@types/node": "^22.14.0", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^6.2.7", "vitest": "^3.1.3"}}