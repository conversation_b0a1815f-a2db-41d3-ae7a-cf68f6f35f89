use super::holdings_calculator::HoldingsCalculator;
use super::snapshot_repository::SnapshotRepositoryTrait;
use crate::accounts::{Account, AccountRepositoryTrait};
use crate::activities::{Activity, ActivityRepositoryTrait};
use crate::constants::{DECIMAL_PRECISION, PORTFOLIO_TOTAL_ACCOUNT_ID};
use crate::errors::{CalculatorError, Error, Result};
use crate::fx::fx_traits::FxServiceTrait;
use crate::portfolio::snapshot::{AccountStateSnapshot, Position};
use crate::utils::time_utils::get_days_between;

use async_trait::async_trait;
use chrono::{Local, NaiveDate, Utc};
use log::{debug, error, info, warn};
use rust_decimal::Decimal;
use std::collections::{BTreeMap, HashMap, HashSet, VecDeque};
use std::sync::{Arc, RwLock};

// --- Service Trait ---
#[async_trait]
pub trait SnapshotServiceTrait: Send + Sync {
    /// Calculates **holdings** snapshots incrementally for the given account IDs, starting from the last calculated date.
    /// If `account_ids` is `None`, calculates for all active accounts AND the "TOTAL" portfolio.
    /// If `account_ids` is `Some`, calculates only for the specified IDs. If "TOTAL" is included, it's calculated from all activities.
    /// If no snapshots exist, it performs a full calculation from the first activity.
    /// Snapshots generated by this method *only contain holdings information* (quantities, costs, cash).
    /// They do NOT contain valuation (market value, base currency conversions, daily gain).
    async fn calculate_holdings_snapshots(&self, account_ids: Option<&[String]>) -> Result<usize>;

    /// Forces a full recalculation of **holdings** snapshots for the given account IDs, deleting existing data first.
    /// If `account_ids` is `None`, recalculates for all active accounts AND the "TOTAL" portfolio.
    /// If `account_ids` is `Some`, recalculates only for the specified IDs. If "TOTAL" is included, it's calculated from all activities.
    /// Snapshots generated by this method *only contain holdings information*.
    async fn force_recalculate_holdings_snapshots(
        &self,
        account_ids: Option<&[String]>,
    ) -> Result<usize>;

    /// Retrieves calculated **holdings** keyframe snapshots for a specific account or the total portfolio within a date range.
    /// Does NOT reconstruct daily snapshots; returns only the saved keyframes.
    fn get_holdings_keyframes(
        &self,
        account_id: &str, // Use specific ID, "TOTAL" for portfolio total
        start_date: Option<NaiveDate>,
        end_date: Option<NaiveDate>,
    ) -> Result<Vec<AccountStateSnapshot>>;

    /// Retrieves **holdings** snapshots for a specific account or the total portfolio within a date range,
    /// reconstructing daily snapshots between saved keyframes by carrying forward holdings.
    /// Valuation fields in the returned snapshots will be zero or default.
    fn get_daily_holdings_snapshots(
        &self,
        account_id: &str, // Use specific ID, "TOTAL" for portfolio total
        start_date: Option<NaiveDate>,
        end_date: Option<NaiveDate>,
    ) -> Result<Vec<AccountStateSnapshot>>;

    /// Retrieves the most recent calculated **holdings** snapshot for a specific account.
    /// Valuation fields will be zero or default.
    fn get_latest_holdings_snapshot(&self, account_id: &str) -> Result<AccountStateSnapshot>;

    /// Calculates and stores aggregated "TOTAL" portfolio snapshots based on individual account holdings.
    /// This should typically be run after `calculate_holdings_snapshots` has processed individual accounts.
    /// It iterates through each day from the earliest activity to the present, generating a TOTAL snapshot
    /// by aggregating individual account snapshots for that day.
    async fn calculate_total_portfolio_snapshots(&self) -> Result<usize>;
}

// --- Service Implementation ---

#[derive(Clone)]
pub struct SnapshotService {
    base_currency: Arc<RwLock<String>>,
    account_repository: Arc<dyn AccountRepositoryTrait>,
    activity_repository: Arc<dyn ActivityRepositoryTrait>,
    snapshot_repository: Arc<dyn SnapshotRepositoryTrait>,
    holdings_calculator: HoldingsCalculator,
}

// Type aliases to simplify function signatures
type AccountsMap = HashMap<String, Account>; // Use HashMap for faster lookup
type ActivitiesVec = Vec<Activity>;
type ActivitiesByAccount = HashMap<String, BTreeMap<NaiveDate, Vec<Activity>>>;
type StartSnapshotsMap = HashMap<String, AccountStateSnapshot>;
type StartDatesMap = HashMap<String, NaiveDate>;

impl SnapshotService {
    pub fn new(
        base_currency: Arc<RwLock<String>>,
        account_repository: Arc<dyn AccountRepositoryTrait>,
        activity_repository: Arc<dyn ActivityRepositoryTrait>,
        snapshot_repository: Arc<dyn SnapshotRepositoryTrait>,
        fx_service: Arc<dyn FxServiceTrait>,
    ) -> Self {
        let holdings_calculator =
            HoldingsCalculator::new(fx_service.clone(), base_currency.clone());
        Self {
            base_currency: base_currency.clone(),
            account_repository,
            activity_repository,
            snapshot_repository,
            holdings_calculator,
        }
    }

    // Create a virtual account object for TOTAL
    fn create_total_virtual_account(&self) -> Account {
        let now = Utc::now().naive_utc();
        Account {
            id: PORTFOLIO_TOTAL_ACCOUNT_ID.to_string(),
            name: "Total Portfolio".to_string(),
            currency: self.base_currency.read().unwrap().clone(),
            is_active: true,                       // Correct field name
            account_type: "AGGREGATE".to_string(), // Indicate it's a special type
            group: None,
            is_default: false,
            created_at: now,
            updated_at: now,
            platform_id: None,
        }
    }

    // --- Core Calculation Logic (Internal Helper) ---
    async fn calculate_holdings_snapshots_internal(
        &self,
        account_ids_param: Option<&[String]>,
        force_full_calculation: bool,
    ) -> Result<usize> {
        debug!(
            "Starting snapshot calculation (Holdings Only) for {:?} accounts. Force full: {}",
            account_ids_param, force_full_calculation
        );

        let (accounts_to_process, all_activities, min_activity_date, calculation_end_date) =
            self.fetch_required_data(account_ids_param)?;

        if accounts_to_process.is_empty() {
            warn!("No accounts found to process.");
            return Ok(0);
        }

        if all_activities.is_empty() && force_full_calculation {
            warn!("No activities found. Clearing snapshots due to force_full_calculation.");
            let ids_to_delete: Vec<String> = accounts_to_process.keys().cloned().collect();
            if !ids_to_delete.is_empty() {
                // If forcing full calc for specific accounts with no activities,
                // we should ensure their snapshots are wiped.
                self.snapshot_repository
                    .delete_snapshots_by_account_ids(&ids_to_delete)
                    .await?;
            }
            return Ok(0);
        } else if all_activities.is_empty() {
            warn!("No activities found for accounts. Calculation will be trivial.");
            return Ok(0);
        }

        let (activities_by_account_date, account_ids_with_activity) = self.preprocess_data(
            &accounts_to_process,
            &all_activities,
            min_activity_date,
            calculation_end_date,
        )?;

        let (start_keyframes, effective_start_dates, calculation_min_date) = self
            .determine_calculation_range_and_initial_state(
                &accounts_to_process,
                &activities_by_account_date,
                &account_ids_with_activity,
                force_full_calculation,
                calculation_end_date,
            )
            .await?;

        let accounts_needing_calculation: AccountsMap = accounts_to_process
            .iter()
            .filter(|(id, _)| effective_start_dates.contains_key(*id))
            .map(|(id, acc)| (id.clone(), acc.clone()))
            .collect();

        if accounts_needing_calculation.is_empty() {
            debug!("No accounts require snapshot calculation in the specified range.");
            return Ok(0);
        }

        let (_final_holdings_states, keyframes_to_save) = self.calculate_daily_holdings_snapshots(
            &accounts_needing_calculation,
            &activities_by_account_date,
            &start_keyframes,
            &effective_start_dates,
            calculation_min_date,
            calculation_end_date,
        )?;

        // Step 8: Persist keyframe snapshots using the new clear method
        for acc_id in accounts_needing_calculation.keys() {
            let frames: Vec<_> = keyframes_to_save
                .iter()
                .filter(|kf| kf.account_id == *acc_id)
                .cloned()
                .collect();

            if force_full_calculation {
                // wipe whole account then insert
                self.snapshot_repository
                    .overwrite_all_snapshots_for_account(acc_id, &frames)
                    .await?;
            } else {
                let start = *effective_start_dates.get(acc_id).unwrap();
                self.snapshot_repository
                    .overwrite_snapshots_for_account_in_range(
                        acc_id,
                        start,
                        calculation_end_date,
                        &frames,
                    )
                    .await?;
            }
        }

        Ok(keyframes_to_save.len())
    }

    // --- Step 1-3: Fetch required data ---
    // Fetches accounts based on `account_ids_param`. If `account_ids_param` is None or contains "TOTAL",
    // fetches ALL active accounts and creates the virtual TOTAL account.
    // Fetches activities ONLY for the relevant accounts (specified or all).
    fn fetch_required_data(
        &self,
        account_ids_param: Option<&[String]>,
    ) -> Result<(AccountsMap, ActivitiesVec, NaiveDate, NaiveDate)> {
        // ── ❶ decide if the caller explicitly asked for the virtual TOTAL ─────────────
        let calculate_total = account_ids_param
            .map(|ids| ids.iter().any(|id| *id == PORTFOLIO_TOTAL_ACCOUNT_ID))
            .unwrap_or(false); // ← no more implicit TOTAL when param == None

        let mut accounts_to_process: AccountsMap = HashMap::new();
        let mut account_ids_to_fetch_activities: Vec<String> = Vec::new();

        // ── ❷ collect *individual* accounts ──────────────────────────────────────────
        match account_ids_param {
            Some(ids) => {
                for id in ids {
                    if id == PORTFOLIO_TOTAL_ACCOUNT_ID {
                        continue;
                    } // TOTAL is virtual
                    if let Ok(acc) = self.account_repository.get_by_id(id) {
                        if acc.is_active {
                            account_ids_to_fetch_activities.push(acc.id.clone());
                            accounts_to_process.insert(acc.id.clone(), acc);
                        }
                    }
                }
            }
            None => {
                for acc in self.account_repository.list(Some(true), None)? {
                    account_ids_to_fetch_activities.push(acc.id.clone());
                    accounts_to_process.insert(acc.id.clone(), acc);
                }
            }
        }

        // ── ❸ add the virtual TOTAL *only* if explicitly requested ───────────────────
        if calculate_total {
            accounts_to_process.insert(
                PORTFOLIO_TOTAL_ACCOUNT_ID.to_string(),
                self.create_total_virtual_account(),
            );
        }

        // ── ❹ pull activities for the collected individual accounts ──────────────────
        let all_activities = if !account_ids_to_fetch_activities.is_empty() {
            self.activity_repository
                .get_activities_by_account_ids(&account_ids_to_fetch_activities)?
        } else {
            Vec::new()
        };

        // unchanged: compute min_activity_date & calculation_end_date …
        let min_activity_date = all_activities
            .iter()
            .map(|a| a.activity_date.naive_utc().date())
            .min()
            .unwrap_or_else(|| Utc::now().naive_utc().date());

        let calculation_end_date = Utc::now().naive_utc().date();

        Ok((
            accounts_to_process,
            all_activities,
            min_activity_date,
            calculation_end_date,
        ))
    }

    // --- Step 5: Preprocess activities ---
    // Adjusts for splits and groups activities.
    // If "TOTAL" account exists in `accounts_to_process`, adds ALL activities to its key.
    fn preprocess_data(
        &self,
        accounts_to_process: &AccountsMap, // Includes virtual TOTAL if needed
        all_activities: &[Activity],
        min_activity_date: NaiveDate,
        calculation_end_date: NaiveDate,
    ) -> Result<(ActivitiesByAccount, HashSet<String>)> {
        // Perform split adjustments on the raw activity list
        let split_factors =
            self.calculate_split_factors(all_activities, min_activity_date, calculation_end_date);
        let adjusted_activities = self.adjust_activities_for_splits(all_activities, &split_factors);

        // Group adjusted activities by original account ID and date
        let mut activities_by_account_date: ActivitiesByAccount = HashMap::new();
        let mut account_ids_with_activity: HashSet<String> = HashSet::new();

        for activity in &adjusted_activities {
            activities_by_account_date
                .entry(activity.account_id.clone())
                .or_default()
                .entry(activity.activity_date.naive_utc().date())
                .or_default()
                .push(activity.clone());
            account_ids_with_activity.insert(activity.account_id.clone());
        }

        // If processing TOTAL (i.e., TOTAL account is present), aggregate all activities under the TOTAL key
        if accounts_to_process.contains_key(PORTFOLIO_TOTAL_ACCOUNT_ID) {
            let mut total_activities_by_date: BTreeMap<NaiveDate, Vec<Activity>> = BTreeMap::new();
            for activity in &adjusted_activities {
                // Use adjusted activities here
                total_activities_by_date
                    .entry(activity.activity_date.naive_utc().date())
                    .or_default()
                    .push(activity.clone());
            }
            if !total_activities_by_date.is_empty() {
                activities_by_account_date.insert(
                    PORTFOLIO_TOTAL_ACCOUNT_ID.to_string(),
                    total_activities_by_date,
                );
                // Mark TOTAL as having activity if any underlying account did
                if !account_ids_with_activity.is_empty() {
                    account_ids_with_activity.insert(PORTFOLIO_TOTAL_ACCOUNT_ID.to_string());
                }
            }
        }

        // Ensure all accounts being processed (even those with no activities) have an entry
        // This simplifies downstream logic that expects keys to exist.
        for acc_id in accounts_to_process.keys() {
            activities_by_account_date
                .entry(acc_id.clone())
                .or_default();
        }

        Ok((activities_by_account_date, account_ids_with_activity))
    }

    // --- Step 6: Determine calculation range and initial state (Keyframes) ---
    // Handles individual accounts and the TOTAL account distinctly.
    async fn determine_calculation_range_and_initial_state(
        &self,
        accounts_to_process: &AccountsMap, // Includes virtual TOTAL if needed
        activities_by_account_date: &ActivitiesByAccount, // Includes TOTAL key if needed
        account_ids_with_activity: &HashSet<String>, // Accounts that actually have activities
        force_full_calculation: bool,
        calculation_end_date: NaiveDate,
    ) -> Result<(StartSnapshotsMap, StartDatesMap, NaiveDate)> {
        debug!(
            "Determining calculation range. Accounts with activity: {:?}. Force full: {}",
            account_ids_with_activity.len(),
            force_full_calculation
        );
        let mut start_keyframes: StartSnapshotsMap = HashMap::new();
        let mut effective_start_dates: StartDatesMap = HashMap::new();
        let mut overall_min_calc_date = calculation_end_date;

        for (acc_id, account) in accounts_to_process {
            if !account_ids_with_activity.contains(acc_id) && !force_full_calculation {
                debug!("Skipping account {} for range determination: no activities and not forcing full.", acc_id);
                continue;
            }

            let min_activity_date_for_account = activities_by_account_date
                .get(acc_id)
                .and_then(|dates_map| dates_map.keys().next().cloned());

            let mut effective_start_date;
            let mut initial_snapshot_for_acc: Option<AccountStateSnapshot> = None;

            if force_full_calculation {
                effective_start_date =
                    min_activity_date_for_account.unwrap_or(calculation_end_date);
                debug!(
                    "Force full calculation: Setting effective_start_date for account {} to {}. Deletion handled by overwrite methods.",
                    acc_id, effective_start_date
                );
            } else {
                if let Some(latest_snapshot) = self
                    .snapshot_repository
                    .get_latest_snapshot_before_date(acc_id, calculation_end_date)?
                {
                    // Re-evaluate the key-frame’s own date without duplicating its activities
                    let snapshot_day = latest_snapshot.snapshot_date;
                    let day_before = snapshot_day.pred_opt().unwrap_or(snapshot_day);

                    // fresh, empty state as of D-1
                    start_keyframes.insert(
                        acc_id.clone(),
                        Self::create_initial_snapshot(account, day_before),
                    );

                    effective_start_date = snapshot_day;
                } else {
                    effective_start_date =
                        min_activity_date_for_account.unwrap_or(calculation_end_date);
                    debug!(
                        "No snapshot found for account {}. Starting from earliest activity: {} or end_date.",
                        acc_id, effective_start_date
                    );
                }
            }

            if let Some(min_act_date) = min_activity_date_for_account {
                if initial_snapshot_for_acc.is_none() && min_act_date < effective_start_date {
                    debug!(
                        "Account {} has activities (at {}) before determined start_date ({}). Adjusting to earliest activity.",
                        acc_id, min_act_date, effective_start_date
                    );
                    effective_start_date = min_act_date;
                    initial_snapshot_for_acc = None;
                }
            }

            if effective_start_date <= calculation_end_date {
                if let Some(snapshot) = initial_snapshot_for_acc {
                    start_keyframes.insert(acc_id.clone(), snapshot);
                } else {
                    let day_before_effective_start = effective_start_date
                        .pred_opt()
                        .unwrap_or(effective_start_date);
                    start_keyframes.insert(
                        acc_id.clone(),
                        Self::create_initial_snapshot(account, day_before_effective_start),
                    );
                }
                effective_start_dates.insert(acc_id.clone(), effective_start_date);
                if effective_start_date < overall_min_calc_date {
                    overall_min_calc_date = effective_start_date;
                }
            } else {
                debug!("Skipping account {} for calculation: effective_start_date {} is after calculation_end_date {}.", 
                       acc_id, effective_start_date, calculation_end_date);
            }
        }

        if effective_start_dates.is_empty() && !accounts_to_process.is_empty() {
            warn!(
                "No effective calculation periods determined for any accounts. Min calc date defaults to end date: {}",
                calculation_end_date
            );
            overall_min_calc_date = calculation_end_date;
        } else if effective_start_dates.is_empty() && accounts_to_process.is_empty() {
            debug!(
                "No accounts to process, min calc date defaults to end date: {}",
                calculation_end_date
            );
            overall_min_calc_date = calculation_end_date;
        }

        Ok((
            start_keyframes,
            effective_start_dates,
            overall_min_calc_date,
        ))
    }

    // --- Step 7: Calculate daily holdings snapshots (in memory) and identify keyframes ---
    // Iterates through dates and calculates holdings for each account needing processing (incl. TOTAL).
    fn calculate_daily_holdings_snapshots(
        &self,
        accounts_needing_calculation: &AccountsMap, // Actual accounts to process
        activities_by_account_date: &ActivitiesByAccount, // Includes TOTAL key if needed
        start_keyframes: &StartSnapshotsMap, // Initial states for accounts needing calculation
        effective_start_dates: &StartDatesMap, // Start dates for accounts needing calculation
        calculation_min_date: NaiveDate,
        calculation_end_date: NaiveDate,
    ) -> Result<(
        HashMap<String, AccountStateSnapshot>, // Final states
        Vec<AccountStateSnapshot>,             // Keyframes to save
    )> {
        let mut current_holdings_snapshots = start_keyframes.clone();
        let mut keyframes_to_save: Vec<AccountStateSnapshot> = Vec::new();
        let date_range = get_days_between(calculation_min_date, calculation_end_date);

        for current_date in date_range {
            // Process only accounts whose effective start date is today or earlier
            let accounts_to_process_today: Vec<_> = accounts_needing_calculation
                .iter()
                .filter(|(id, _)| {
                    effective_start_dates
                        .get(*id)
                        .map_or(false, |start_date| *start_date <= current_date)
                })
                .collect();

            if accounts_to_process_today.is_empty() {
                // This shouldn't happen if calculation_min_date was determined correctly, but handle defensively.
                debug!(
                    "No accounts to process for date {}. Skipping day.",
                    current_date
                );
                continue;
            }

            let mut next_day_holdings_snapshots =
                HashMap::with_capacity(accounts_to_process_today.len());
            let mut keyframes_today = Vec::new();

            for (account_id, _account) in accounts_to_process_today {
                let previous_holdings_snapshot = current_holdings_snapshots
                    .get(account_id)
                     .ok_or_else(|| {
                         error!("CRITICAL: Missing previous holdings snapshot for account {} in memory map for date {}", account_id, current_date);
                         Error::Calculation(CalculatorError::Calculation(format!(
                             "Missing previous holdings snapshot for account {} for date {}",
                             account_id, current_date
                         )))
                     })?;

                // Get activities for THIS specific account (or the aggregated TOTAL activities)
                let activities_today = activities_by_account_date
                    .get(account_id) // Fetches individual or TOTAL activities based on account_id
                    .and_then(|date_map| date_map.get(&current_date))
                    .cloned()
                    .unwrap_or_default();

                let is_first_day = effective_start_dates.get(account_id) == Some(&current_date);
                let has_activities = !activities_today.is_empty();

                let current_holdings_snapshot: AccountStateSnapshot; // Final state for today

                if !has_activities {
                    // No activities today, just carry forward the previous state
                    let mut carried_forward_state = previous_holdings_snapshot.clone();
                    carried_forward_state.snapshot_date = current_date;
                    carried_forward_state.id =
                        format!("{}_{}", account_id, current_date.format("%Y-%m-%d"));
                    // Note: calculated_at remains the same as the previous snapshot
                    current_holdings_snapshot = carried_forward_state;
                    debug!(
                        "No activities for account {} on {}. Carrying forward state.",
                        account_id, current_date
                    );
                } else {
                    // Activities occurred, call the calculator
                    match self.holdings_calculator.calculate_next_holdings(
                        previous_holdings_snapshot,
                        &activities_today, // Pass the already fetched activities
                        current_date,
                    ) {
                        Ok(calculated_snapshot) => {
                            // Calculator provides the new state, including updated calculated_at
                            current_holdings_snapshot = calculated_snapshot;
                            debug!(
                                "Holdings calculated successfully for account {} on {}",
                                account_id, current_date
                            );
                        }
                        Err(e) => {
                            error!(
                                "Holdings calculation failed for account {} on {}: {}. Carrying forward previous state.",
                                account_id, current_date, e
                            );
                            // Carry forward the previous day's state on error
                            let mut errored_state = previous_holdings_snapshot.clone();
                            errored_state.snapshot_date = current_date; // Update date even if carried forward
                            errored_state.id =
                                format!("{}_{}", account_id, current_date.format("%Y-%m-%d"));
                            // calculated_at remains the same as the previous snapshot
                            current_holdings_snapshot = errored_state;
                        }
                    }
                }

                // Decide if it's a keyframe based on the determined snapshot
                // A keyframe is needed on the first day of calculation or if activities happened.
                let is_keyframe = is_first_day || has_activities;

                if is_keyframe {
                    debug!(
                        "Keyframe identified for account {} on {} (Holdings Only) (First day: {}, Has activities: {})",
                        account_id, current_date, is_first_day, has_activities
                    );
                    // Create the keyframe based on the final state for today
                    let mut keyframe_snapshot = current_holdings_snapshot.clone();
                    // Ensure account_id and id are correctly set for the keyframe
                    keyframe_snapshot.account_id = account_id.clone();
                    keyframe_snapshot.id =
                        format!("{}_{}", account_id, current_date.format("%Y-%m-%d"));
                    keyframes_today.push(keyframe_snapshot);
                }

                // Store the calculated/carried-forward snapshot for the next iteration's "previous" state
                next_day_holdings_snapshots
                    .insert(account_id.to_string(), current_holdings_snapshot); // Use the final determined snapshot
            }

            // Update the main state map for the next day
            // Important: Only update states for accounts processed today.
            for (id, state) in next_day_holdings_snapshots {
                current_holdings_snapshots.insert(id, state);
            }
            keyframes_to_save.extend(keyframes_today);
        }

        // Return the final holdings states and the identified keyframes
        Ok((current_holdings_snapshots, keyframes_to_save))
    }

    // Renamed and refined from the previous aggregate_total_portfolio_snapshot
    fn generate_total_portfolio_snapshot_for_date(
        &self,
        target_date: NaiveDate,
        // Map of Account ID -> AccountStateSnapshot for all *individual* accounts as of target_date
        individual_snapshots_on_date: &HashMap<String, AccountStateSnapshot>,
        base_portfolio_currency: &str,
    ) -> Result<AccountStateSnapshot> {
        debug!(
            "Generating aggregated TOTAL portfolio snapshot for date: {}",
            target_date
        );

        let mut aggregated_cash_balances: HashMap<String, Decimal> = HashMap::new();
        let mut aggregated_positions: HashMap<String, Position> = HashMap::new(); // Position struct from crate::portfolio::snapshot::Position
        let mut overall_cost_basis_base_ccy = Decimal::ZERO;
        let mut overall_net_contribution_base_ccy = Decimal::ZERO;

        for (individual_acc_id, individual_snapshot) in individual_snapshots_on_date {
            // Ensure we are only processing individual accounts here, not an old TOTAL snapshot if it exists in the input map
            if individual_acc_id == PORTFOLIO_TOTAL_ACCOUNT_ID {
                continue;
            }

            // 1. Aggregate Cash Balances
            // Iterate over all cash balances in the individual snapshot (which might be multi-currency)
            // and add them to the corresponding currency in the aggregated map.
            for (currency, amount) in &individual_snapshot.cash_balances {
                *aggregated_cash_balances
                    .entry(currency.clone())
                    .or_insert(Decimal::ZERO) += *amount;
            }

            // 2. Aggregate Net Contribution (already frozen FX)
            overall_net_contribution_base_ccy += individual_snapshot.net_contribution_base;

            // 3. Aggregate Positions & Calculate Overall Cost Basis for TOTAL (in base_portfolio_currency)
            for (_pos_asset_id, pos) in &individual_snapshot.positions {
                let agg_pos = aggregated_positions
                    .entry(pos.asset_id.clone())
                    .or_insert_with(|| Position {
                        id: format!("{}_{}", pos.asset_id, PORTFOLIO_TOTAL_ACCOUNT_ID),
                        account_id: PORTFOLIO_TOTAL_ACCOUNT_ID.to_string(),
                        asset_id: pos.asset_id.clone(),
                        quantity: Decimal::ZERO,
                        average_cost: Decimal::ZERO,
                        total_cost_basis: Decimal::ZERO, // This will be in asset's currency (pos.currency)
                        currency: pos.currency.clone(),
                        lots: VecDeque::new(), // Lots are generally not merged for TOTAL view
                        inception_date: pos.inception_date,
                        created_at: Utc::now(),
                        last_updated: Utc::now(),
                    });

                agg_pos.quantity += pos.quantity;
                agg_pos.total_cost_basis += pos.total_cost_basis; // Summing in asset's currency

                // Convert this specific position's total_cost_basis (in asset currency) to base_portfolio_currency
                // and add to the portfolio's overall cost_basis.
                match self
                    .holdings_calculator
                    .fx_service
                    .convert_currency_for_date(
                        pos.total_cost_basis,
                        &pos.currency,
                        base_portfolio_currency,
                        target_date,
                    ) {
                    Ok(converted_pos_cost_basis) => {
                        overall_cost_basis_base_ccy += converted_pos_cost_basis;
                    }
                    Err(e) => {
                        warn!(
                            "Failed to convert position cost basis for asset {} ({} {} to {}) for TOTAL on {}: {}. Adding unconverted.",
                            pos.asset_id, pos.total_cost_basis, pos.currency, base_portfolio_currency, target_date, e
                        );
                        if pos.currency != base_portfolio_currency {
                            overall_cost_basis_base_ccy += pos.total_cost_basis;
                        // Fallback
                        } else {
                            overall_cost_basis_base_ccy += pos.total_cost_basis;
                            // Already in base
                        }
                    }
                }
            }
        }

        // Finalize average costs for aggregated positions
        for agg_pos in aggregated_positions.values_mut() {
            if !agg_pos.quantity.is_zero() {
                agg_pos.average_cost =
                    (agg_pos.total_cost_basis / agg_pos.quantity).round_dp(DECIMAL_PRECISION);
            } else {
                agg_pos.average_cost = Decimal::ZERO;
                agg_pos.total_cost_basis = Decimal::ZERO;
            }
        }

        Ok(AccountStateSnapshot {
            id: format!(
                "{}_{}",
                PORTFOLIO_TOTAL_ACCOUNT_ID,
                target_date.format("%Y-%m-%d")
            ),
            account_id: PORTFOLIO_TOTAL_ACCOUNT_ID.to_string(),
            snapshot_date: target_date,
            currency: base_portfolio_currency.to_string(), // TOTAL snapshot is denominated in base currency
            cash_balances: aggregated_cash_balances, // Itemized by account currency holding the cash
            positions: aggregated_positions,
            cost_basis: overall_cost_basis_base_ccy.round_dp(DECIMAL_PRECISION),
            net_contribution: overall_net_contribution_base_ccy.round_dp(DECIMAL_PRECISION),
            net_contribution_base: overall_net_contribution_base_ccy.round_dp(DECIMAL_PRECISION),
            calculated_at: Utc::now().naive_utc(),
        })
    }

    // --- Helpers ---

    // create_initial_snapshot creates a snapshot with default values
    fn create_initial_snapshot(account: &Account, date: NaiveDate) -> AccountStateSnapshot {
        AccountStateSnapshot {
            id: format!("{}_{}", account.id, date.format("%Y-%m-%d")),
            account_id: account.id.clone(),
            snapshot_date: date,
            currency: account.currency.clone(),
            positions: HashMap::new(),
            cash_balances: HashMap::new(),
            cost_basis: Decimal::ZERO,
            net_contribution: Decimal::ZERO,
            net_contribution_base: Decimal::ZERO,
            calculated_at: Utc::now().naive_utc(),
        }
    }

    // (Helper function group_activities_by_account_and_date moved inside preprocess_data)

    fn calculate_split_factors(
        &self,
        activities: &[Activity],
        start_date: NaiveDate,
        end_date: NaiveDate,
    ) -> HashMap<String, Vec<(NaiveDate, Decimal)>> {
        use crate::activities::activities_constants::ACTIVITY_TYPE_SPLIT;
        let mut split_factors: HashMap<String, Vec<(NaiveDate, Decimal)>> = HashMap::new();
        for activity in activities.iter().filter(|a| {
            a.activity_type == ACTIVITY_TYPE_SPLIT
                && a.activity_date.naive_utc().date() >= start_date
                && a.activity_date.naive_utc().date() <= end_date
        }) {
            // Check if the activity amount exists and represents a valid positive split ratio
            if let Some(split_ratio) = activity.amount {
                if split_ratio.is_sign_positive() && !split_ratio.is_zero() {
                    // Collect valid splits (date, ratio) for the asset
                    split_factors
                        .entry(activity.asset_id.clone())
                        .or_default() // Get the Vec, create if needed
                        .push((activity.activity_date.naive_utc().date(), split_ratio));
                // Push (date, ratio) tuple
                } else {
                    // Log warning for invalid ratio (e.g., zero or negative)
                    warn!(
                        "Invalid split ratio {} for Split activity {} on {}. Ignoring split.",
                        split_ratio, activity.id, activity.activity_date
                    );
                }
            } else {
                // Log warning if amount is missing for a split activity
                warn!(
                    "Missing amount for Split activity {} for asset {} on {}. Ignoring split.",
                    activity.id, activity.asset_id, activity.activity_date
                );
            }
        }
        for splits in split_factors.values_mut() {
            splits.sort_by_key(|k| k.0);
        }
        split_factors
    }

    fn adjust_activities_for_splits(
        &self,
        activities: &[Activity],
        split_factors: &HashMap<String, Vec<(NaiveDate, Decimal)>>,
    ) -> Vec<Activity> {
        use crate::activities::activities_constants::ACTIVITY_TYPE_SPLIT;

        let mut adjusted_activities = Vec::with_capacity(activities.len());
        for activity in activities {
            let mut adj_activity = activity.clone();
            if let Some(splits) = split_factors.get(&activity.asset_id) {
                // Do not adjust the SPLIT activity itself, only others
                if adj_activity.activity_type != ACTIVITY_TYPE_SPLIT {
                    let mut cumulative_factor = Decimal::ONE;
                    // Apply splits that happened *after* the activity date
                    for (split_date, split_ratio) in splits.iter() {
                        // Iterate chronologically
                        if *split_date > adj_activity.activity_date.naive_utc().date() {
                            // Split happened after this activity, need to adjust past quantity/price
                            cumulative_factor *= split_ratio;
                        }
                    }

                    if cumulative_factor != Decimal::ONE {
                        debug!(
                            "Adjusting activity {} on {} for asset {} due to future splits. Factor: {}",
                            adj_activity.id, adj_activity.activity_date.naive_utc().date(), adj_activity.asset_id, cumulative_factor
                        );
                        // Adjust quantity
                        // Use correct field name 'quantity'
                        adj_activity.quantity =
                            (activity.quantity * cumulative_factor).round_dp(DECIMAL_PRECISION);

                        // Adjust unit price (inverse factor)
                        // Use correct field name 'unit_price'
                        if !activity.unit_price.is_zero() {
                            if !cumulative_factor.is_zero() {
                                // Avoid division by zero
                                // Use correct field name 'unit_price'
                                adj_activity.unit_price = (activity.unit_price / cumulative_factor)
                                    .round_dp(DECIMAL_PRECISION);
                            } else {
                                warn!("Cumulative split factor is zero for activity {}. Cannot adjust unit price.", adj_activity.id);
                                // Use correct field name 'unit_price'
                                adj_activity.unit_price = Decimal::ZERO; // Or handle as error?
                            }
                        }
                    }
                }
            }
            adjusted_activities.push(adj_activity);
        }
        adjusted_activities
    }

    // --- New method to calculate and store TOTAL portfolio snapshots ---
    async fn calculate_total_portfolio_snapshots_impl(&self) -> Result<usize> {
        debug!("Starting calculation of TOTAL portfolio snapshots (based on stored individual keyframes).");

        let active_accounts = self.account_repository.list(Some(true), None)?;
        if active_accounts.is_empty() {
            warn!("No active accounts found. Cannot generate TOTAL snapshots.");
            self.snapshot_repository
                .overwrite_all_snapshots_for_account(PORTFOLIO_TOTAL_ACCOUNT_ID, &[])
                .await?;
            return Ok(0);
        }

        let all_individual_keyframes = self
            .snapshot_repository
            .get_all_active_account_snapshots(None, None)?;

        if all_individual_keyframes.is_empty() {
            warn!("No keyframes found for any active individual accounts. Cannot generate TOTAL snapshots.");
            self.snapshot_repository
                .overwrite_all_snapshots_for_account(PORTFOLIO_TOTAL_ACCOUNT_ID, &[])
                .await?;
            info!("Cleaned any existing TOTAL snapshots as no new ones were generated.");
            return Ok(0);
        }

        let mut keyframes_by_account: HashMap<String, BTreeMap<NaiveDate, AccountStateSnapshot>> =
            HashMap::new();
        let mut all_snapshot_dates: HashSet<NaiveDate> = HashSet::new();

        for keyframe in all_individual_keyframes {
            if keyframe.account_id == PORTFOLIO_TOTAL_ACCOUNT_ID {
                continue;
            }
            all_snapshot_dates.insert(keyframe.snapshot_date);
            keyframes_by_account
                .entry(keyframe.account_id.clone())
                .or_default()
                .insert(keyframe.snapshot_date, keyframe);
        }

        if all_snapshot_dates.is_empty() {
            info!("No individual account keyframes found after processing. Cannot generate TOTAL snapshots.");
            return Ok(0);
        }

        let base_portfolio_currency = self.base_currency.read().unwrap().clone();
        let mut total_portfolio_snapshots_to_save: Vec<AccountStateSnapshot> = Vec::new();

        let mut sorted_snapshot_dates: Vec<NaiveDate> = all_snapshot_dates.into_iter().collect();
        sorted_snapshot_dates.sort();

        for target_date in sorted_snapshot_dates {
            let mut individual_snapshots_on_or_before_date: HashMap<String, AccountStateSnapshot> =
                HashMap::new();

            for (account_id, account_keyframes) in &keyframes_by_account {
                if let Some((_, latest_snapshot)) = account_keyframes.range(..=target_date).last() {
                    individual_snapshots_on_or_before_date
                        .insert(account_id.clone(), latest_snapshot.clone());
                }
            }

            if !individual_snapshots_on_or_before_date.is_empty() {
                match self.generate_total_portfolio_snapshot_for_date(
                    target_date,
                    &individual_snapshots_on_or_before_date,
                    &base_portfolio_currency,
                ) {
                    Ok(total_snapshot) => {
                        total_portfolio_snapshots_to_save.push(total_snapshot);
                    }
                    Err(e) => {
                        error!(
                            "Failed to generate TOTAL portfolio snapshot for target_date {}: {}",
                            target_date, e
                        );
                    }
                }
            }
        }

        if !total_portfolio_snapshots_to_save.is_empty() {
            info!(
                "Saving {} new TOTAL portfolio snapshots.",
                total_portfolio_snapshots_to_save.len()
            );
            self.snapshot_repository
                .overwrite_all_snapshots_for_account(
                    PORTFOLIO_TOTAL_ACCOUNT_ID,
                    &total_portfolio_snapshots_to_save,
                )
                .await?;
            Ok(total_portfolio_snapshots_to_save.len())
        } else {
            warn!("No TOTAL portfolio snapshots were generated to save. Deleting existing TOTAL snapshots anyway if any existed.");
            self.snapshot_repository
                .overwrite_all_snapshots_for_account(PORTFOLIO_TOTAL_ACCOUNT_ID, &[])
                .await?;
            info!("Cleaned any existing TOTAL snapshots as no new ones were generated.");
            Ok(0)
        }
    }
}

#[async_trait]
impl SnapshotServiceTrait for SnapshotService {
    async fn calculate_holdings_snapshots(&self, account_ids: Option<&[String]>) -> Result<usize> {
        self.calculate_holdings_snapshots_internal(account_ids, false)
            .await
    }

    async fn force_recalculate_holdings_snapshots(
        &self,
        account_ids: Option<&[String]>,
    ) -> Result<usize> {
        self.calculate_holdings_snapshots_internal(account_ids, true)
            .await
    }

    fn get_holdings_keyframes(
        &self,
        account_id: &str,
        start_date_opt: Option<NaiveDate>,
        end_date_opt: Option<NaiveDate>,
    ) -> Result<Vec<AccountStateSnapshot>> {
        debug!(
            "Getting saved holdings keyframes for {} from {:?} to {:?}",
            account_id, start_date_opt, end_date_opt
        );
        // Directly fetch from the repository without reconstruction
        self.snapshot_repository
            .get_snapshots_by_account(account_id, start_date_opt, end_date_opt)
    }

    fn get_daily_holdings_snapshots(
        &self,
        account_id: &str,
        start_date_opt: Option<NaiveDate>,
        end_date_opt: Option<NaiveDate>,
    ) -> Result<Vec<AccountStateSnapshot>> {
        debug!(
            "Reconstructing daily holdings snapshots for {} from {:?} to {:?}",
            account_id, start_date_opt, end_date_opt
        );
        let end_date = end_date_opt.unwrap_or_else(|| Local::now().date_naive());

        // Determine start date: Use provided, else earliest, else end_date
        let start_date = match start_date_opt {
            Some(date) => date,
            None => {
                match self
                    .snapshot_repository
                    .get_earliest_snapshot_date(account_id)
                {
                    Ok(Some(date)) => date,
                    _ => {
                        warn!("No earliest snapshot found for account {}. Using end date {} as start date.", account_id, end_date);
                        end_date // Default to end date if no earliest found
                    }
                }
            }
        };

        if start_date > end_date {
            warn!(
                "get_daily_holdings_snapshots: Start date {} is after end date {}. Returning empty.",
                start_date, end_date
            );
            return Ok(Vec::new());
        }

        // Fetch keyframes within the actual range first, as we might need them to determine the initial state.
        let keyframes_in_range = self.snapshot_repository.get_snapshots_by_account(
            account_id,
            Some(start_date), // Fetch keyframes from start_date...
            Some(end_date),   // ...to end_date inclusive
        )?;
        let keyframes_map: BTreeMap<NaiveDate, AccountStateSnapshot> = keyframes_in_range
            .into_iter()
            .map(|kf| (kf.snapshot_date, kf))
            .collect();

        // Try to get the state from the day before the loop starts.
        let initial_state_result = self
            .snapshot_repository
            .get_latest_snapshot_before_date(account_id, start_date);

        let mut current_state = match initial_state_result? {
            Some(initial_snapshot) => initial_snapshot,
            None => {
                // No snapshot found before start date.
                // If there are no keyframes at all in the requested range, we can't reconstruct.
                if keyframes_map.is_empty() {
                    debug!("No snapshot found before start date {} and no keyframes in range for account {}. Returning empty.", start_date, account_id);
                    return Ok(Vec::new());
                }

                // Otherwise, history starts within our date range. We create a default "empty" state
                // for the day before the loop, and the loop will then pick up the first keyframe correctly.
                let account_details =
                    self.account_repository.get_by_id(account_id).or_else(|_| {
                        if account_id == PORTFOLIO_TOTAL_ACCOUNT_ID {
                            Ok(self.create_total_virtual_account())
                        } else {
                            Err(Error::Repository(format!(
                                "Account not found while reconstructing daily snapshots: {}",
                                account_id
                            )))
                        }
                    })?;
                let day_before_start = start_date.pred_opt().unwrap_or(start_date);
                Self::create_initial_snapshot(&account_details, day_before_start)
            }
        };

        let capacity = (end_date - start_date).num_days().try_into().unwrap_or(0) + 1;
        let mut reconstructed_snapshots = Vec::with_capacity(capacity);
        let date_range = get_days_between(start_date, end_date);

        for current_date in date_range {
            if let Some(saved_keyframe) = keyframes_map.get(&current_date) {
                // Use the saved keyframe for this date
                current_state = saved_keyframe.clone();
                reconstructed_snapshots.push(saved_keyframe.clone());
            } else {
                // Reconstruct by carrying forward the previous day's state
                let mut reconstructed = current_state.clone();
                reconstructed.snapshot_date = current_date;
                reconstructed.id = format!(
                    "{}_{}",
                    reconstructed.account_id,
                    current_date.format("%Y-%m-%d")
                );
                // Removed lines attempting to reset non-existent valuation fields
                reconstructed.calculated_at = Utc::now().naive_utc(); // Mark when it was reconstructed

                current_state = reconstructed.clone(); // Update state for the next day
                reconstructed_snapshots.push(reconstructed);
            }
        }

        Ok(reconstructed_snapshots)
    }

    fn get_latest_holdings_snapshot(&self, account_id: &str) -> Result<AccountStateSnapshot> {
        let today = Utc::now().naive_utc().date();
        // The date passed to get_latest_snapshot_before_date is exclusive, so use tomorrow to include today.
        let tomorrow = today.succ_opt().unwrap_or(today);
        match self
            .snapshot_repository
            .get_latest_snapshot_before_date(account_id, tomorrow)?
        {
            Some(snapshot) => Ok(snapshot),
            None => {
                // It's possible no snapshot exists yet, which is not necessarily an error,
                // but we should inform the caller.
                debug!(
                    "No snapshot found for account {} on or before {}",
                    account_id, today
                );
                Err(Error::Repository(format!(
                    "No holdings snapshot found for account {}",
                    account_id
                )))
            }
        }
    }

    async fn calculate_total_portfolio_snapshots(&self) -> Result<usize> {
        self.calculate_total_portfolio_snapshots_impl().await
    }
}
