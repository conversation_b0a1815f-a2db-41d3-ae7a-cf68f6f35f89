[package]
name = "wealthfolio_core"
version = "1.1.5"
description = "An Open Source Desktop Portfolio tracker"
authors = ["<PERSON>"]
license = "AGPL-3.0"
repository = "https://github.com/afadil/wealthfolio"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
diesel = { version = "2.2", features = ["sqlite", "chrono", "r2d2", "numeric", "returning_clauses_for_sqlite_3_35"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.10", features = ["v4"] }
rusqlite = { version = "0.34", features = ["bundled"] }
csv = "1.3"
yahoo_finance_api = "4.0"
regex = "1.10"
reqwest = { version = "0.12", features = ["json", "cookies" ] }
thiserror = "1.0"
lazy_static = "1.5"
diesel_migrations = { version = "2.2", features = ["sqlite" ] }
r2d2 = "0.8"
dashmap = "6.1"
async-trait = "0.1"
rust_decimal = { version = "1.37", features = ["maths","serde-float"] }
num-traits = "0.2"
rust_decimal_macros = "1.37"
log = "0.4"
futures = "0.3"
serde_with = "3.4"
tokio = { version = "1", features = ["macros", "rt-multi-thread", "sync"] }

[dev-dependencies]
