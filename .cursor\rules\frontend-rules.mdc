---
description: frontend and typescript rules
globs: src/, *.tsx, *.ts
---
# Frontend Development Guidelines

## Tech Stack
- Node.js
- React
- Vite
- TanStack Query
- TanStack Router
- Tailwind CSS

## Code Style and Structure

### General Principles
- Write concise, technical TypeScript code following industry best practices
- Avoid code duplication; use functions and modules for reusable logic
- Use functional and declarative programming patterns
- Avoid classes
- Use descriptive variable names with auxiliary verbs (e.g., `isLoading`, `hasError`)

### File Structure
1. Exported component
2. Subcomponents
3. Helpers
4. Static content
5. Types

### Naming Conventions
- Use lowercase with dashes for directories (e.g., `components/auth-wizard`)
- Favor named exports for components

### TypeScript Usage
- Use TypeScript for all code
- Prefer interfaces over types
- Avoid enums; use maps instead
- Use functional components with TypeScript interfaces

### Syntax and Formatting
- Use the `function` keyword for pure functions
- Use curly braces for all conditionals
- Favor simplicity over cleverness
- Use declarative JSX

### UI and Styling
- Use Tailwind for components and styling

### Performance Optimization
Focus on:
- Immutable data structures
- Efficient data fetching strategies
- Network request optimization
- Efficient data structures and algorithms
- Efficient rendering strategies
- Optimized state management