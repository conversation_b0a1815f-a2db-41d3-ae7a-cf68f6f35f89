[package]
name = "wealthfolio-app"
version = "1.1.5"
description = "Portfolio tracker"
authors = ["<PERSON>"]
license = "AGPL-3.0"
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.1.1", features = [] }

[dependencies]
wealthfolio_core = { path = "../src-core" }
tauri = { version = "2.4.1", features = [] }
diesel = { version = "2.2", features = ["sqlite", "chrono", "r2d2", "numeric", "returning_clauses_for_sqlite_3_35"] }
dotenvy = "0.15.7"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0.128"
chrono = { version = "0.4.38", features = ["serde"] }
tauri-plugin-fs = "2.2.1"
tauri-plugin-dialog = "2.2.1"
tauri-plugin-shell = "2.2.1"
tauri-plugin-log = "2.3.1"
log = "0.4"
futures = "0.3"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
appstore = [] # Feature flag for App Store builds

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-updater = "2.7.0"
tauri-plugin-window-state = "2"
