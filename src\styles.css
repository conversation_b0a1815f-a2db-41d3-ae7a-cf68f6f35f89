@import '@fontsource/ibm-plex-mono';
@import '@fontsource/ibm-plex-mono/200.css';
@import '@fontsource/ibm-plex-mono/300.css';
@import '@fontsource/ibm-plex-mono/400.css';
@import '@fontsource/ibm-plex-mono/500.css';
@import '@fontsource/ibm-plex-mono/600.css';
@import '@fontsource/ibm-plex-mono/700.css';
@import '@fontsource/ibm-plex-mono/400-italic.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Flexoki Light Theme Variables */
    --flexoki-bg: 48 100% 97%;
    --flexoki-bg-2: 51 59% 95%;
    --flexoki-ui: 51 21% 88%;
    --flexoki-ui-2: 50 14% 83%;
    --flexoki-ui-3: 55 10% 79%;
    --flexoki-tx: 0 3% 6%;
    --flexoki-tx-2: 50 3% 42%;
    --flexoki-tx-3: 49 7% 70%;
    --flexoki-re: 3 62% 42%;
    --flexoki-gr: 73 84% 27%;
    --flexoki-ye: 45 99% 34%;

    /* Shadcn Theme Mapping */
    --background: var(--flexoki-bg);
    --foreground: var(--flexoki-tx);
    --card: var(--flexoki-bg-2);
    --card-foreground: var(--flexoki-tx);
    --popover: var(--flexoki-bg);
    --popover-foreground: var(--flexoki-tx);
    --primary: var(--flexoki-tx);
    --primary-foreground: var(--flexoki-bg);
    --secondary: var(--flexoki-ui-2);
    --secondary-foreground: var(--flexoki-tx-2);
    --muted: var(--flexoki-ui);
    --muted-foreground: var(--flexoki-tx-2);
    --accent: var(--flexoki-ui);
    --accent-foreground: var(--flexoki-tx-2);
    --destructive: var(--flexoki-re);
    --destructive-foreground: var(--flexoki-bg);
    --success: var(--flexoki-gr);
    --success-foreground: var(--flexoki-bg);
    --warning: var(--flexoki-ye);
    --warning-foreground: var(--flexoki-bg);
    --border: var(--flexoki-ui);
    --input: var(--flexoki-ui-2);
    --ring: var(--flexoki-ui-3);
    --radius: 0.5rem;

    /* Chart Colors */
    --chart-1: 40 3% 20%; /* #1C1B1A - Darkest */
    --chart-2: 45 2% 33%; /* #575653 - More distinct from 2 */
    --chart-3: 50 3% 42%; /* #6F6E6B */
    --chart-4: 43 3% 52%; /* #878580 */
    --chart-5: 47 4% 61%; /* #9F9D96 */
    --chart-6: 49 7% 70%; /* #B7B5AC */
    --chart-7: 55 10% 79%; /* #CECDC3 */
    --chart-8: 50 14% 83%; /* #E6E4D9 */
    --chart-9: 51 21% 88%; /* #F2F0E5 */
  }

  .dark {
    /* Flexoki Dark Theme Variables */
    --flexoki-bg: 0 3% 6%;
    --flexoki-bg-2: 30 4% 11%;
    --flexoki-ui: 30 3% 15%;
    --flexoki-ui-2: 40 3% 20%;
    --flexoki-ui-3: 30 3% 24%;
    --flexoki-tx: 55 10% 79%;
    --flexoki-tx-2: 43 3% 52%;
    --flexoki-tx-3: 45 2% 33%;
    --flexoki-re: 5 61% 54%;
    --flexoki-gr: 72 46% 41%;
    --flexoki-ye: 45 82% 45%;

    /* Chart Colors */
    --chart-1: 25 92% 72%;
    --chart-2: 24 81% 61%;
    --chart-3: 23 70% 51%;
    --chart-4: 23 73% 46%;
    --chart-5: 22 80% 41%;
    --chart-6: 22 82% 34%;
    --chart-7: 22 79% 25%;
    --chart-8: 22 75% 20%;
    --chart-9: 22 70% 15%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}


number-flow-react::part(left),
number-flow-react::part(right),
number-flow-react::part(left)::after,
number-flow-react::part(right)::after,
number-flow-react::part(symbol) {
  padding: calc(var(--number-flow-mask-height, 0.25em) / 2) 0;
}


number-flow-react.muted-fraction::part(decimal),
number-flow-react.muted-fraction::part(fraction) {
  color: hsl(var(--muted-foreground));
}